'use client';

import React, { useState } from 'react';
import { createStyles } from 'antd-style';
import {
  Card,
  Button,
  Input,
  Progress,
  Space,
  message,
  Divider,
  Tooltip,
  Dropdown,
  Table,
  Modal,
  Upload
} from 'antd';
import {
  Play,
  Square,
  Download,
  Pause,
  PlayCircle,
  RotateCcw,
  Trash2,
  Plus,
  Upload as UploadIcon
} from 'lucide-react';
import { useBatchStore } from '@/store/batch';
import { BatchItem } from '@/types';

const { TextArea } = Input;

const useStyles = createStyles(({ token, css }) => ({
  container: css`
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 24px;
    background: ${token.colorBgLayout};
    overflow-y: auto;
  `,

  promptCard: css`
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;

    .ant-card-head {
      background: ${token.colorBgContainer};
      border-bottom: 1px solid ${token.colorBorderSecondary};
    }

    .ant-card-body {
      padding: 20px;
    }
  `,

  controlPanel: css`
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;

    @media (min-width: 768px) {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .controls-left {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .controls-right {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  `,

  progressSection: css`
    background: ${token.colorBgContainer};
    padding: 16px;
    border-radius: 8px;
    border: 1px solid ${token.colorBorderSecondary};
    margin-bottom: 16px;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .progress-text {
        font-size: 14px;
        color: ${token.colorTextSecondary};
        font-weight: 500;
      }

      .progress-stats {
        font-size: 13px;
        color: ${token.colorTextTertiary};
      }
    }
  `,

  tableContainer: css`
    flex: 1;
    background: ${token.colorBgContainer};
    border: 1px solid ${token.colorBorderSecondary};
    border-radius: 8px;
    overflow: visible;
    min-height: 500px;
    max-height: none;

    .ant-card-head {
      background: ${token.colorBgContainer};
      border-bottom: 1px solid ${token.colorBorderSecondary};
    }

    .ant-card-body {
      padding: 0;
      height: auto;
      min-height: 450px;
    }

    .ant-table-wrapper {
      height: auto;
      min-height: 450px;

      .ant-table-container {
        height: 100%;
      }

      .ant-table-body {
        height: calc(100% - 55px);
        overflow-y: auto;
      }
    }
  `,

  statusTag: css`
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: none;

    &.pending {
      background: ${token.colorWarningBg};
      color: ${token.colorWarning};
    }

    &.generating {
      background: ${token.colorPrimaryBg};
      color: ${token.colorPrimary};
    }

    &.completed {
      background: ${token.colorSuccessBg};
      color: ${token.colorSuccess};
    }

    &.failed {
      background: ${token.colorErrorBg};
      color: ${token.colorError};
    }
  `,
  
  inputCell: css`
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  `,
  
  outputCell: css`
    max-width: 400px;

    .output-content {
      height: 400px;
      overflow-y: auto;
      font-size: ${token.fontSizeSM}px;
      line-height: 1.5;
      padding: 8px;
      background: ${token.colorFillAlter};
      border-radius: 4px;
      border: 1px solid ${token.colorBorder};
      white-space: pre-wrap;
      word-break: break-word;

      /* 自定义滚动条 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: ${token.colorBorderSecondary};
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: ${token.colorBorder};
      }
    }
  `,
}));

const BatchGenerator: React.FC = () => {
  const { styles } = useStyles();
  const [prompt, setPrompt] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newInput, setNewInput] = useState('');
  const [newVariables, setNewVariables] = useState<Record<string, string>>({});

  // 使用批量生成状态管理
  const {
    items,
    isGenerating,
    progress,
    isPaused,
    addBatchItem,
    removeBatchItem,
    clearBatchItems,
    generateBatch,
    generateSingle,
    stopGeneration,
    pauseGeneration,
    resumeGeneration,
    addBatchTexts,
    exportResults,
    exportBatchResults,
  } = useBatchStore();

  // 添加新的输入项
  const addItem = () => {
    if (!prompt.trim()) {
      message.warning('请先输入提示词');
      return;
    }

    addBatchItem({
      prompt: prompt.trim(),
      variables: newVariables,
      status: 'pending',
    });

    setNewVariables({});
    setIsModalVisible(false);
    message.success('添加成功');
  };

  // 批量导入
  const handleBatchImport = (text: string) => {
    if (!prompt.trim()) {
      message.warning('请先输入提示词');
      return;
    }

    // 使用新的批量文本处理功能
    addBatchTexts(prompt.trim(), text);

    // 计算导入的项目数量
    const trimmedText = text.trim();
    let count = 0;

    if (/第(?:[一二三四五六七八九十百千万]+|\d+)集：/.test(trimmedText)) {
      // 如果包含"第*集："格式，计算集数
      const matches = trimmedText.match(/第(?:[一二三四五六七八九十百千万]+|\d+)集：/g);
      count = matches ? matches.length : 0;
      message.success(`检测到"第*集"格式，成功导入 ${count} 集内容`);
    } else {
      // 按空行分割计算
      const lines = trimmedText.split(/\n\s*\n/).filter(line => line.trim());
      count = lines.length;
      message.success(`成功导入 ${count} 个项目`);
    }
  };

  // 开始生成
  const startGeneration = async () => {
    if (items.length === 0) {
      message.warning('请添加至少一个项目');
      return;
    }

    await generateBatch();
  };



  // 处理导出
  const handleExport = async (format: 'json' | 'csv' | 'txt' | 'docx') => {
    try {
      if (format === 'docx') {
        await exportBatchResults(format);
      } else {
        exportResults(format);
      }
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 处理批量导出
  const handleBatchExport = async (format: 'txt' | 'docx', version: 'standard' | 'director' | 'combined' | 'separate') => {
    try {
      // 将 'standard' 和 'director' 映射到支持的模式
      const mode = (version === 'standard' || version === 'director') ? 'combined' : version;
      await exportBatchResults(format, mode);
    } catch (error) {
      message.error('导出失败');
    }
  };

  // 计算进度百分比
  const progressPercent = progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0;

  // 表格列定义
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_: unknown, __: unknown, index: number) => index + 1,
    },
    {
      title: '提示词',
      dataIndex: 'prompt',
      key: 'prompt',
      ellipsis: true,
      render: (text: string) => (
        <div className={styles.inputCell} title={text}>
          {text}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <span className={`${styles.statusTag} ${status}`}>
          {status === 'pending' && '等待中'}
          {status === 'generating' && '生成中'}
          {status === 'completed' && '已完成'}
          {status === 'failed' && '失败'}
        </span>
      ),
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      render: (text: string) => (
        <div className={styles.outputCell}>
          {text && (
            <div className="output-content" title={text}>
              {text}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_: unknown, record: BatchItem) => (
        <Space size="small">
          <Tooltip title="重新生成">
            <Button
              type="text"
              size="small"
              icon={<RotateCcw size={14} />}
              onClick={() => generateSingle(record.id)}
              disabled={isGenerating}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              size="small"
              icon={<Trash2 size={14} />}
              onClick={() => removeBatchItem(record.id)}
              disabled={isGenerating}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.container}>
      {/* 提示词设置 */}
      <Card title="提示词设置" className={styles.promptCard}>
        <TextArea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="请输入批量生成的提示词模板，例如：请帮我翻译以下内容：{input}"
          rows={3}
          disabled={isGenerating}
        />
      </Card>

      {/* 控制面板 */}
      <div className={styles.controlPanel}>
        <div className="controls-left">
          <Button
            type="primary"
            icon={<Play size={16} />}
            onClick={startGeneration}
            disabled={isGenerating || items.length === 0}
            loading={isGenerating}
          >
            {isGenerating ? '生成中...' : '开始生成'}
          </Button>

          <Button
            icon={<Square size={16} />}
            onClick={stopGeneration}
            disabled={!isGenerating}
          >
            停止
          </Button>

          <Button
            onClick={clearBatchItems}
            disabled={isGenerating}
          >
            清空列表
          </Button>
        </div>

        <div className="controls-right">
          <Button
            icon={<Plus size={16} />}
            onClick={() => setIsModalVisible(true)}
            disabled={isGenerating}
          >
            添加项目
          </Button>

          <Dropdown
            menu={{
              items: [
                {
                  key: 'basic-export',
                  label: '基础导出',
                  children: [
                    { key: 'txt', label: 'TXT 格式', onClick: () => handleExport('txt') },
                    { key: 'docx', label: 'DOCX 格式', onClick: () => handleExport('docx') },
                    { key: 'json', label: 'JSON 格式', onClick: () => handleExport('json') },
                    { key: 'csv', label: 'CSV 格式', onClick: () => handleExport('csv') },
                  ]
                },
                {
                  key: 'batch-export',
                  label: '批量导出',
                  children: [
                    { key: 'standard-txt', label: '标准版 (TXT)', onClick: () => handleBatchExport('txt', 'standard') },
                    { key: 'standard-docx', label: '标准版 (DOCX)', onClick: () => handleBatchExport('docx', 'standard') },
                    { key: 'combined-txt', label: '完整合集 (TXT)', onClick: () => handleBatchExport('txt', 'combined') },
                    { key: 'combined-docx', label: '完整合集 (DOCX)', onClick: () => handleBatchExport('docx', 'combined') },
                    { key: 'separate-txt', label: '分离文件 (TXT)', onClick: () => handleBatchExport('txt', 'separate') },
                    { key: 'separate-docx', label: '分离文件 (DOCX)', onClick: () => handleBatchExport('docx', 'separate') },
                  ]
                }
              ]
            }}
            disabled={items.filter(item => item.status === 'completed').length === 0}
          >
            <Button icon={<Download size={16} />}>
              导出结果
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* 进度显示 */}
      {(isGenerating || isPaused) && progress.total > 0 && (
        <div className={styles.progressSection}>
          <div className="progress-info">
            <span className="progress-text">
              批量生成进度 {isPaused && <span style={{ color: '#faad14' }}>(已暂停)</span>}
            </span>
            <span className="progress-stats">
              {progress.current} / {progress.total} 已完成
            </span>
          </div>
          <Progress
            percent={progressPercent}
            status={isPaused ? 'normal' : isGenerating ? 'active' : 'normal'}
            strokeColor={isPaused ? '#faad14' : {
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
        </div>
      )}

      {/* 数据表格 */}
      <Card title={`批量项目 (${items.length})`} className={styles.tableContainer}>
        <Table
          columns={columns}
          dataSource={items}
          rowKey="id"
          pagination={false}
          scroll={{ x: 800, y: 800 }}
          size="small"
        />
      </Card>

      {/* 添加项目弹窗 */}
      <Modal
        title="添加项目"
        open={isModalVisible}
        onOk={addItem}
        onCancel={() => {
          setIsModalVisible(false);
          setNewInput('');
          setNewVariables({});
        }}
        okText="添加"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <label>输入内容：</label>
          <TextArea
            value={newInput}
            onChange={(e) => setNewInput(e.target.value)}
            placeholder="请输入要处理的内容"
            rows={3}
          />
        </div>

        <div style={{ marginBottom: 16 }}>
          <label>变量 (可选)：</label>
          <Input
            placeholder="变量名"
            value={Object.keys(newVariables)[0] || ''}
            onChange={(e) => {
              const key = e.target.value;
              if (key) {
                setNewVariables({ [key]: newInput });
              } else {
                setNewVariables({});
              }
            }}
            style={{ marginBottom: 8 }}
          />
        </div>

        <Divider />

        <Upload
          beforeUpload={(file) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              const text = e.target?.result as string;
              handleBatchImport(text);
              setIsModalVisible(false);
            };
            reader.readAsText(file);
            return false;
          }}
          showUploadList={false}
          accept=".txt,.csv"
        >
          <Button icon={<UploadIcon size={16} />} block>
            从文件批量导入
          </Button>
        </Upload>
      </Modal>
    </div>
  );
};

export default BatchGenerator;